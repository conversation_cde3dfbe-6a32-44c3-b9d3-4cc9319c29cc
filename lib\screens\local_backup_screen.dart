import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../l10n/app_localizations.dart';

import '../providers/ccp_accounts_provider.dart';
import '../services/local_backup_service.dart';
import '../widgets/custom_button.dart';

class LocalBackupScreen extends StatefulWidget {
  const LocalBackupScreen({super.key});

  @override
  State<LocalBackupScreen> createState() => _LocalBackupScreenState();
}

class _LocalBackupScreenState extends State<LocalBackupScreen> {
  final LocalBackupService _backupService = LocalBackupService();
  bool _isLoading = false;
  String? _importData;
  final TextEditingController _importController = TextEditingController();

  @override
  void dispose() {
    _importController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);
    final accountsProvider = Provider.of<CCPAccountsProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          loc.localBackupTitle ?? 'Local Backup',
          style: const TextStyle(fontSize: 16),
        ),
        titleSpacing: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Export section
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.upload_file,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              loc.localBackupExport ?? 'Export Accounts',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontSize: 16,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        loc.localBackupExportDescription ??
                        'Export your accounts to a file that you can save on your device or share with other apps.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 13,
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton(
                          onPressed: _isLoading ? null : () => _handleExport(accountsProvider),
                          text: loc.localBackupExportButton ?? 'Export',
                          icon: Icons.share,
                          isLoading: _isLoading,
                          type: ButtonType.export,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Import section
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.download,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              loc.localBackupImport ?? 'Import Accounts',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontSize: 16,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Sélectionnez un fichier JSON à importer :',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 13,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Bouton unique: Sélectionner un fichier
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton(
                          onPressed: _isLoading ? null : () => _handleImportFromFile(accountsProvider),
                          text: loc.localBackupImportButton ?? 'Sélectionner un fichier',
                          icon: Icons.upload_file,
                          isLoading: _isLoading,
                          type: ButtonType.import,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Information note
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withAlpha(50),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        loc.localBackupNote ??
                        'Note: Importing accounts will not delete your existing accounts. New accounts will be added to your existing ones.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleExport(CCPAccountsProvider accountsProvider) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = accountsProvider.accounts;
      final success = await _backupService.exportAccounts(accounts, context);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).localBackupExportSuccess ??
              'Accounts exported successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).localBackupExportError ??
              'Failed to export accounts',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleImport(CCPAccountsProvider accountsProvider) async {
    if (_importData == null || _importData!.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            AppLocalizations.of(context).localBackupImportConfirmTitle ??
            'Import Accounts',
          ),
          content: Text(
            AppLocalizations.of(context).localBackupImportConfirmMessage ??
            'This will add the accounts from the backup to your existing accounts. Continue?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                AppLocalizations.of(context).cancel ?? 'Cancel',
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                AppLocalizations.of(context).confirm ?? 'Confirm',
              ),
            ),
          ],
        ),
      );

      if (confirmed != true) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final accounts = await _backupService.importAccountsFromJson(_importData!);

      if (accounts != null && accounts.isNotEmpty && mounted) {
        // Add each account individually
        int successCount = 0;
        for (final account in accounts) {
          try {
            await accountsProvider.addAccount(account);
            successCount++;
          } catch (e) {
            debugPrint('Error adding account: $e');
          }
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                successCount > 0
                    ? '${AppLocalizations.of(context).localBackupImportSuccess ?? 'Successfully imported'} $successCount ${successCount == 1 ? 'account' : 'accounts'}'
                    : AppLocalizations.of(context).localBackupImportNoAccounts ?? 'No new accounts were imported',
              ),
              backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
            ),
          );

          // Clear the import field
          _importController.clear();
          setState(() {
            _importData = null;
          });
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).localBackupImportError ??
              'Failed to import accounts',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Import depuis un fichier sélectionné
  Future<void> _handleImportFromFile(CCPAccountsProvider accountsProvider) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Sélectionner un fichier JSON
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        // Lire le contenu du fichier
        final file = File(result.files.single.path!);
        final jsonData = await file.readAsString();

        // Importer les données
        await _importAccountsFromJson(jsonData, accountsProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'import: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  // Méthode commune pour importer les comptes depuis JSON
  Future<void> _importAccountsFromJson(String jsonData, CCPAccountsProvider accountsProvider) async {
    try {
      // Afficher le dialog de confirmation
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Importer les comptes'),
          content: const Text(
            'Ceci ajoutera les comptes de la sauvegarde à vos comptes existants. Continuer ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('ANNULER'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('CONFIRMER'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      final accounts = await _backupService.importAccountsFromJson(jsonData);

      if (accounts != null && accounts.isNotEmpty && mounted) {
        // Ajouter chaque compte individuellement
        int successCount = 0;
        for (final account in accounts) {
          try {
            await accountsProvider.addAccount(account);
            successCount++;
          } catch (e) {
            debugPrint('Error adding account: $e');
          }
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                successCount > 0
                    ? 'Importé avec succès $successCount ${successCount == 1 ? 'compte' : 'comptes'}'
                    : 'Aucun nouveau compte n\'a été importé',
              ),
              backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Échec de l\'importation des comptes'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
