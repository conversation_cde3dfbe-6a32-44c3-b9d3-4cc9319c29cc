flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"  # Logo principal pour l'icône d'installation
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/logo.png"  # Logo principal pour l'icône d'installation
  min_sdk_android: 21
  # Configuration pour ajuster l'icône avec un peu d'espace
  adaptive_icon_padding: true  # Active le padding automatique
  remove_alpha_ios: true  # Supprime la transparence pour iOS
  remove_alpha_channel: true  # Supprime la transparence pour Android
  image_path_android: "assets/images/logo.png"  # Logo principal pour Android
  web:
    generate: true
    image_path: "assets/images/logo.png"  # Utilisation du logo original
    background_color: "#FFFFFF"
    theme_color: "#FFFFFF"
  windows:
    generate: true
    image_path: "assets/images/logo.png"  # Utilisation du logo original
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/logo.png"  # Utilisation du logo original
