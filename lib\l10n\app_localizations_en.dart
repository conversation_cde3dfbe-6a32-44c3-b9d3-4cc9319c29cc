// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'CCP RIP Calculator';

  @override
  String get bottomNavCalculate => 'Calculate';

  @override
  String get bottomNavAccounts => 'Accounts';

  @override
  String get bottomNavVerify => 'Verify';

  @override
  String get homeScreenTitle => 'CCP RIP Calculator';

  @override
  String get savedAccountsScreenTitle => 'Saved Accounts';

  @override
  String get verifyRipScreenTitle => 'Verify RIP Account';

  @override
  String get menuPrivacyPolicy => 'Privacy Policy';

  @override
  String get menuRateApp => 'Rate App';

  @override
  String get menuUpdate => 'Update';

  @override
  String get menuAbout => 'About';

  @override
  String get menuQuit => 'Quit';

  @override
  String get menuCloudBackup => 'Cloud Backup';

  @override
  String get menuLocalBackup => 'Local Backup';

  @override
  String get cloudBackupTitle => 'Cloud Backup';

  @override
  String get cloudBackupInfo => 'About Cloud Backup';

  @override
  String get cloudBackupDescription =>
      'Cloud backup allows you to save your CCP accounts to Google Drive. This way, you can restore your accounts if you change devices or reinstall the app.';

  @override
  String get cloudBackupPrivacy =>
      'Your data is stored privately in your Google Drive account. Only you have access to it.';

  @override
  String get cloudBackupSignInRequired =>
      'Sign in to Google Drive to use cloud backup';

  @override
  String get cloudBackupSignIn => 'Sign in with Google';

  @override
  String get cloudBackupSignOut => 'Sign out';

  @override
  String get cloudBackupLastBackup => 'Last backup';

  @override
  String get cloudBackupBackup => 'Backup';

  @override
  String get cloudBackupRestore => 'Restore';

  @override
  String get cloudBackupSuccess => 'Accounts backed up successfully';

  @override
  String get cloudBackupRestoreSuccess => 'Accounts restored successfully';

  @override
  String get cloudBackupRestoreConfirmTitle => 'Restore Accounts';

  @override
  String get cloudBackupRestoreConfirmMessage =>
      'This will replace your current accounts with the ones from your last backup. Continue?';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get localBackupTitle => 'Backup & Restore';

  @override
  String get localBackupExport => 'Export Your Accounts';

  @override
  String get localBackupExportDescription =>
      'Create a secure backup of all your CCP accounts in a file that you can safely store on your device or share.';

  @override
  String get localBackupExportButton => 'Export My Accounts';

  @override
  String get localBackupExportSuccess =>
      '✅ Your accounts have been exported successfully';

  @override
  String get localBackupExportError =>
      '❌ Unable to export your accounts. Please try again.';

  @override
  String get localBackupImport => 'Import Accounts';

  @override
  String get localBackupImportDescription =>
      'Restore your accounts from a previously created backup file. Select the JSON file containing your data.';

  @override
  String get localBackupImportHint => 'Paste JSON content here';

  @override
  String get localBackupImportButton => 'Select File';

  @override
  String get localBackupImportFilePrompt => 'Select a JSON file to import:';

  @override
  String get localBackupImportConfirmTitle => 'Confirm Import';

  @override
  String get localBackupImportConfirmMessage =>
      'This action will add the accounts from the backup file to your current list. Your existing accounts will be preserved. Do you want to continue?';

  @override
  String localBackupImportSuccess(Object count) {
    return '✅ $count account(s) imported successfully';
  }

  @override
  String get localBackupImportNoAccounts =>
      'ℹ️ No new accounts found in the file';

  @override
  String get localBackupImportError =>
      '❌ Unable to import accounts. Please verify the file is valid.';

  @override
  String get localBackupNote =>
      'Note: Importing accounts will not delete your existing accounts. New accounts will be added to your existing ones.';

  @override
  String get verifyRipScannerTooltip => 'Scan RIP Code';

  @override
  String get changeLanguageTooltip => 'Change Language';

  @override
  String get verifyRipHeaderTitle => 'RIP Account Verification';

  @override
  String get verifyRipHeaderSubtitle =>
      'Enter an RIP code to check its validity';

  @override
  String get verifyRipInputLabel => 'RIP Account';

  @override
  String get verifyRipInputHint => 'Format: ********xxxxxxxxxxxx';

  @override
  String get verifyRipPasteTooltip => 'Paste from clipboard';

  @override
  String get verifyRipButtonText => 'VERIFY';

  @override
  String get verifyRipValidationEmpty => 'Please enter an RIP account';

  @override
  String get verifyRipValidationLength => 'RIP code must be exactly 20 digits';

  @override
  String get verifyRipValidationPrefix => 'RIP code must start with ********';

  @override
  String get verifyRipResultValid => 'Valid RIP Account';

  @override
  String get verifyRipResultInvalid => 'Invalid RIP Account';

  @override
  String get verifyRipResultValidSub =>
      'The RIP code was successfully verified';

  @override
  String get verifyRipResultInvalidSub => 'The entered RIP code is incorrect';

  @override
  String get verifyRipInfoTitle => 'Account Information';

  @override
  String get verifyRipInfoPrefixLabel => 'Prefix';

  @override
  String get verifyRipInfoBankCodeLabel => 'Bank Code';

  @override
  String get verifyRipInfoCcpNumberLabel => 'CCP Number';

  @override
  String get verifyRipInfoCcpKeyLabel => 'CCP Key';

  @override
  String get verifyRipInfoRipKeyLabel => 'RIP Key';

  @override
  String get verifyRipFullRipLabel => 'RIP Account';

  @override
  String get verifyRipCopyButton => 'Copy';

  @override
  String get verifyRipCopiedMessage => 'RIP Account copied';

  @override
  String get verifyRipGenericInvalidMessage =>
      'The entered RIP account is incorrect. Please check and try again.';

  @override
  String get changeLanguageDialogTitle => 'Change Language';

  @override
  String get ccpFormInputLabel => 'CCP Number';

  @override
  String get ccpFormInputHint => 'Enter CCP number';

  @override
  String get ccpFormValidationEmpty => 'Please enter a CCP number';

  @override
  String get ccpFormValidationInvalid => 'Please enter a valid CCP number';

  @override
  String get ccpFormResultTitle => 'Calculation successful';

  @override
  String get ccpFormResultSubtitle => 'Your RIP code is ready to use';

  @override
  String get ccpFormCcpKeyLabel => 'CCP Key';

  @override
  String get ccpFormRipKeyLabel => 'RIP Key';

  @override
  String get ccpFormRipAccountLabel => 'RIP Account';

  @override
  String get ccpFormSaveButton => 'SAVE ACCOUNT';

  @override
  String get ccpFormSaveButtonShort => 'SAVE';

  @override
  String get ccpFormAccountExistsError =>
      'An account with this RIP code already exists';

  @override
  String get ccpFormClearTooltip => 'Clear';

  @override
  String get savedAccountsImportTooltip => 'Import accounts from file';

  @override
  String get savedAccountsExportTooltip => 'Export all my accounts';

  @override
  String get savedAccountsSearchLabel => 'Search';

  @override
  String get savedAccountsSearchHint => 'Search by name or CCP number';

  @override
  String get savedAccountsEmptySearch => 'No accounts found';

  @override
  String get savedAccountsEmpty => 'No saved accounts';

  @override
  String get savedAccountsEditDialogTitle => 'Edit Account';

  @override
  String get savedAccountsEditOwnerNameLabel => 'Owner\'s name:';

  @override
  String get savedAccountsEditOwnerNameHint => 'Enter name';

  @override
  String get savedAccountsDialogCancel => 'CANCEL';

  @override
  String get savedAccountsDialogSave => 'SAVE';

  @override
  String savedAccountsImportSuccess(int count) {
    return '$count accounts imported successfully';
  }

  @override
  String savedAccountsImportError(String error) {
    return 'Error importing accounts: $error';
  }

  @override
  String savedAccountsExportError(String error) {
    return 'Error exporting accounts: $error';
  }

  @override
  String get savedAccountsManageTitle => 'Account Management';

  @override
  String get savedAccountsImportTitle => 'Import Accounts';

  @override
  String get savedAccountsExportTitle => 'Export My Accounts';

  @override
  String get saveAccountScreenTitle => 'Save Account';

  @override
  String get saveAccountOwnerNameHint => 'Enter owner\'s name';

  @override
  String get saveAccountOwnerNameValidation => 'Please enter the owner\'s name';

  @override
  String get saveAccountSuccessMessage => 'Account saved successfully';

  @override
  String get saveAccountGenericErrorPrefix => 'Error: ';

  @override
  String get savedAccountLastModified => 'Modified on:';

  @override
  String get savedAccountCcpLabel => 'CCP:';

  @override
  String get savedAccountRipLabel => 'RIP Account:';

  @override
  String get savedAccountCopyRip => 'Copy RIP';

  @override
  String get savedAccountShare => 'Share';

  @override
  String get savedAccountEdit => 'Edit';

  @override
  String get savedAccountDelete => 'Delete';

  @override
  String get savedAccountRipCopied => 'RIP Account copied';

  @override
  String get savedAccountDeleteTitle => 'Delete';

  @override
  String savedAccountDeleteConfirm(String ownerName) {
    return 'Delete $ownerName\'s account?';
  }

  @override
  String get savedAccountQrTitle => 'Scan QR Code';

  @override
  String get savedAccountQrClose => 'Close';

  @override
  String get savedAccountSaveButton => 'Save Account';

  @override
  String get verifyRipCopyButtonText => 'Copy';

  @override
  String get verifyRipSaveButtonText => 'Save';

  @override
  String get ccpFormInputInstruction =>
      'Enter a CCP code without key (maximum 10 digits)';

  @override
  String get appInfoButtonText => 'More information';

  @override
  String get appInfoScreenTitle => 'About CCP RIP DZ';

  @override
  String get appInfoContent =>
      '📄 About CCP RIP DZ\nWelcome to the CCP RIP DZ app!\nOur app provides a simple and effective solution for managing CCP accounts in Algeria. Here\'s what you can do with it:\n\n🔧 Key Features:\n\n✅ RIP Calculation: \nEasily calculate the RIP (Postal Identity Statement) from your CCP number without the key.\n\n💾 Save Accounts: \nStore calculated accounts for quick access later.\n\n📤 Easy Sharing:\n    - Copy the RIP code \n    - Generate a QR Code \n    - Share the account through your favorite apps \n\n🔍 RIP Validation: \nCheck the validity of an existing RIP code:\n\n     - By entering it manually \n     - Or scanning a QR Code';

  @override
  String get ccpCalculationSuccess => 'Calculation completed successfully';

  @override
  String get ccpCalculationReady => 'Your RIP code is ready to use';

  @override
  String get ccpKeyCcp => 'CCP Key';

  @override
  String get ccpKeyRip => 'RIP Key';

  @override
  String get ccpAccountRip => 'RIP Account';

  @override
  String get ccpCopyButton => 'Copy';

  @override
  String get ccpCopiedMessage => 'Copied';

  @override
  String get ccpSaveButton => 'SAVE ACCOUNT';
}
