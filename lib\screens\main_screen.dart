import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';

import '../l10n/app_localizations.dart';
import '../providers/locale_provider.dart';
import '../services/update_checker_service.dart';

import 'home_screen.dart';
import 'local_backup_screen.dart';
import 'privacy_policy_screen.dart';
import 'saved_accounts_screen.dart';
import 'verify_rip_screen.dart';

// Enum for menu choices (excluding direct language items now)
enum AppMenuChoice { privacy, rate, update, about, localBackup, quit }

// Top-level function to build the general PopupMenuButton
Widget buildAppPopupMenu(BuildContext context) {
  final loc = AppLocalizations.of(context);

  return PopupMenuButton<AppMenuChoice>(
    onSelected: (choice) => handleAppMenuChoice(context, choice),
    itemBuilder: (BuildContext context) => <PopupMenuEntry<AppMenuChoice>>[
      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.privacy,
        child: ListTile(
          leading: const Icon(Icons.privacy_tip),
          title: Text(loc.menuPrivacyPolicy),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuDivider(height: 1),
      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.rate,
        child: ListTile(
          leading: const Icon(Icons.star),
          title: Text(loc.menuRateApp),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuDivider(height: 1),
      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.update,
        child: ListTile(
          leading: const Icon(Icons.system_update),
          title: Text(loc.menuUpdate),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuDivider(height: 1),
      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.about,
        child: ListTile(
          leading: const Icon(Icons.info),
          title: Text(loc.menuAbout),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),

      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.localBackup,
        child: ListTile(
          leading: const Icon(Icons.save_alt),
          title: Text(loc.menuLocalBackup),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),

      const PopupMenuDivider(height: 1),
      PopupMenuItem<AppMenuChoice>(
        value: AppMenuChoice.quit,
        child: ListTile(
          leading: const Icon(Icons.exit_to_app),
          title: Text(loc.menuQuit),
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ),
    ],
  );
}

// Fonction pour récupérer la version de l'application
Future<String> _getAppVersion() async {
  try {
    final packageInfo = await PackageInfo.fromPlatform();
    return 'Version ${packageInfo.version}';
  } catch (e) {
    return 'Version 2.1.0';
  }
}

// Top-level function to handle general menu selections
void handleAppMenuChoice(BuildContext context, AppMenuChoice choice) async {
  // final localeProvider = Provider.of<LocaleProvider>(context, listen: false); // Not needed here anymore for lang
  bool isMounted() => Navigator.of(context).mounted;

  switch (choice) {
    case AppMenuChoice.privacy:
      if (!isMounted()) return;
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
      );
      break;
    case AppMenuChoice.rate:
      final Uri rateUrl = Uri.parse('https://play.google.com/store/apps/details?id=com.my.cleribccp');
      if (await canLaunchUrl(rateUrl)) {
        await launchUrl(rateUrl, mode: LaunchMode.externalApplication);
      } else {
        if (!isMounted()) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Impossible d\'ouvrir le lien de notation')), // TODO: Localize
        );
      }
      break;
    case AppMenuChoice.update:
      // Vérifier manuellement les mises à jour
      final updateChecker = UpdateCheckerService();
      final hasUpdate = await updateChecker.checkForUpdate();

      if (!isMounted()) return;

      if (hasUpdate) {
        // Il y a une mise à jour disponible, afficher le dialog
        final latestVersion = await updateChecker.getLatestVersionFromPlayStore();
        if (latestVersion != null && isMounted()) {
          updateChecker.showUpdateDialog(context, latestVersion);
        }
      } else {
        // Aucune mise à jour disponible
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).updateNoUpdateAvailable ??
                         'Vous avez déjà la dernière version'),
            backgroundColor: Colors.green,
          ),
        );
      }
      break;


    case AppMenuChoice.localBackup:
      if (!isMounted()) return;
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const LocalBackupScreen()),
      );
      break;

    case AppMenuChoice.about:
      if (!isMounted()) return;
      final version = await _getAppVersion();
      if (!isMounted()) return;
      showDialog(
        context: context,
        builder: (BuildContext context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // En-tête avec dégradé
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withAlpha(179), // 0.7 * 255 = 179
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                  child: Row(
                    children: [
                      Image.asset(
                        'assets/images/logo.png',
                        width: 48, // Beaucoup plus grand
                        height: 48,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Localizations.localeOf(context).languageCode == 'en'
                                  ? 'CCP_RIP'
                                  : Localizations.localeOf(context).languageCode == 'ar'
                                      ? 'CCP_RIP'
                                      : 'CCP_RIP',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                version,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Contenu
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 14),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Développeur
                      Row(
                        children: [
                          const Icon(
                            Icons.code,
                            size: 16,
                            color: Color(0xFF0D47A1),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              Localizations.localeOf(context).languageCode == 'en'
                                  ? 'Developed by Dzair Emprienties'
                                  : Localizations.localeOf(context).languageCode == 'ar'
                                      ? 'تم تطويره بواسطة Dzair Emprienties'
                                      : 'Développé par Dzair Emprienties',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Description
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.info_outline,
                            size: 16,
                            color: Color(0xFF0D47A1),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              Localizations.localeOf(context).languageCode == 'en'
                                  ? 'Mobile application to calculate and verify CCP/RIP codes in Algeria.'
                                  : Localizations.localeOf(context).languageCode == 'ar'
                                      ? 'تطبيق محمول لحساب والتحقق من رموز CCP/RIP في الجزائر.'
                                      : 'Application mobile pour calculer et vérifier les codes CCP/RIP en Algérie.',
                              style: const TextStyle(
                                fontSize: 12,
                                height: 1.3,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Fonctionnalités
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.check_circle_outline,
                            size: 16,
                            color: Color(0xFF0D47A1),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              Localizations.localeOf(context).languageCode == 'en'
                                  ? 'Calculate RIP keys, verify accounts, and save your CCP accounts for quick access.'
                                  : Localizations.localeOf(context).languageCode == 'ar'
                                      ? 'حساب مفاتيح RIP، التحقق من الحسابات، وحفظ حسابات CCP للوصول السريع.'
                                      : 'Calculer les clés RIP, vérifier les comptes et enregistrer vos comptes CCP pour un accès rapide.',
                              style: const TextStyle(
                                fontSize: 12,
                                height: 1.3,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Bouton
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      Localizations.localeOf(context).languageCode == 'en'
                          ? 'Close'
                          : Localizations.localeOf(context).languageCode == 'ar'
                              ? 'إغلاق'
                              : 'Fermer',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
      break;
    case AppMenuChoice.quit:
      SystemNavigator.pop();
      break;
  }
}

// Top-level function to show language selection dialog
void showLanguageSelectionDialog(BuildContext context) {
  final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
  showDialog(
    context: context,
    builder: (BuildContext dialogContext) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(12),
                child: Text(
                  AppLocalizations.of(context).changeLanguageDialogTitle,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const Divider(height: 1),
              _LanguageOption(
                flag: '🇺🇸',
                title: 'English',
                onTap: () {
                  localeProvider.setLocale(const Locale('en'));
                  Navigator.of(dialogContext).pop();
                },
              ),
              const Divider(height: 1),
              _LanguageOption(
                flag: '🇫🇷',
                title: 'Français',
                onTap: () {
                  localeProvider.setLocale(const Locale('fr'));
                  Navigator.of(dialogContext).pop();
                },
              ),
              const Divider(height: 1),
              _LanguageOption(
                flag: '🇩🇿',
                title: 'العربية',
                onTap: () {
                  localeProvider.setLocale(const Locale('ar'));
                  Navigator.of(dialogContext).pop();
                },
              ),
            ],
          ),
        ),
      );
    },
  );
}

class _LanguageOption extends StatelessWidget {
  final String flag;
  final String title;
  final VoidCallback onTap;

  const _LanguageOption({
    required this.flag,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                flag,
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  final int? initialIndex;
  const MainScreen({super.key, this.initialIndex});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _currentIndex;

  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex ?? 0;
    _screens = [
      const HomeScreen(),
      const SavedAccountsScreen(),
      const VerifyRIPScreen(),
    ];

    // Vérifier automatiquement les mises à jour au démarrage
    WidgetsBinding.instance.addPostFrameCallback((_) {
      UpdateCheckerService().checkForUpdateOnStartup(context);
    });
  }



  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is int) {
      if (_currentIndex != args) {
        setState(() {
          _currentIndex = args;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Builder(
        builder: (navContext) {
          final navLoc = AppLocalizations.of(navContext);
          return BottomNavigationBar(
            currentIndex: _currentIndex,
            selectedFontSize: 12,
            unselectedFontSize: 11,
            selectedItemColor: Theme.of(context).primaryColor,
            unselectedItemColor: Colors.grey,
            type: BottomNavigationBarType.fixed,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            items: [
              BottomNavigationBarItem(
                icon: const Icon(Icons.calculate),
                label: navLoc.bottomNavCalculate,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.list),
                label: navLoc.bottomNavAccounts,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.check_circle),
                label: navLoc.bottomNavVerify,
              ),
            ],
          );
        }
      ),
    );
  }
}
