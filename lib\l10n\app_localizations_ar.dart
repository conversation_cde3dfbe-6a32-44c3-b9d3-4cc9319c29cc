// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'حاسبة CCP RIP';

  @override
  String get bottomNavCalculate => 'حساب';

  @override
  String get bottomNavAccounts => 'الحسابات';

  @override
  String get bottomNavVerify => 'تحقق';

  @override
  String get homeScreenTitle => 'حاسبة CCP RIP';

  @override
  String get savedAccountsScreenTitle => 'الحسابات المحفوظة';

  @override
  String get verifyRipScreenTitle => 'التحقق من حساب RIP';

  @override
  String get menuPrivacyPolicy => 'سياسة الخصوصية';

  @override
  String get menuRateApp => 'تقييم التطبيق';

  @override
  String get menuUpdate => 'تحديث';

  @override
  String get menuAbout => 'حول';

  @override
  String get menuQuit => 'خروج';

  @override
  String get menuCloudBackup => 'النسخ الاحتياطي السحابي';

  @override
  String get menuLocalBackup => 'النسخ الاحتياطي المحلي';

  @override
  String get cloudBackupTitle => 'النسخ الاحتياطي السحابي';

  @override
  String get cloudBackupInfo => 'حول النسخ الاحتياطي السحابي';

  @override
  String get cloudBackupDescription =>
      'يتيح لك النسخ الاحتياطي السحابي حفظ حسابات CCP الخاصة بك على Google Drive. بهذه الطريقة، يمكنك استعادة حساباتك إذا قمت بتغيير الأجهزة أو إعادة تثبيت التطبيق.';

  @override
  String get cloudBackupPrivacy =>
      'يتم تخزين بياناتك بشكل خاص في حساب Google Drive الخاص بك. أنت فقط من يمكنه الوصول إليها.';

  @override
  String get cloudBackupSignInRequired =>
      'قم بتسجيل الدخول إلى Google Drive لاستخدام النسخ الاحتياطي السحابي';

  @override
  String get cloudBackupSignIn => 'تسجيل الدخول باستخدام Google';

  @override
  String get cloudBackupSignOut => 'تسجيل الخروج';

  @override
  String get cloudBackupLastBackup => 'آخر نسخة احتياطية';

  @override
  String get cloudBackupBackup => 'نسخ احتياطي';

  @override
  String get cloudBackupRestore => 'استعادة';

  @override
  String get cloudBackupSuccess => 'تم حفظ الحسابات بنجاح';

  @override
  String get cloudBackupRestoreSuccess => 'تمت استعادة الحسابات بنجاح';

  @override
  String get cloudBackupRestoreConfirmTitle => 'استعادة الحسابات';

  @override
  String get cloudBackupRestoreConfirmMessage =>
      'سيؤدي هذا إلى استبدال حساباتك الحالية بتلك الموجودة في النسخة الاحتياطية الأخيرة. هل تريد المتابعة؟';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get localBackupTitle => 'النسخ الاحتياطي والاستعادة';

  @override
  String get localBackupExport => 'تصدير حساباتك';

  @override
  String get localBackupExportDescription =>
      'إنشاء نسخة احتياطية آمنة لجميع حسابات CCP الخاصة بك في ملف يمكنك حفظه بأمان على جهازك أو مشاركته.';

  @override
  String get localBackupExportButton => 'تصدير حساباتي';

  @override
  String get localBackupExportSuccess => '✅ تم تصدير حساباتك بنجاح';

  @override
  String get localBackupExportError =>
      '❌ تعذر تصدير حساباتك. يرجى المحاولة مرة أخرى.';

  @override
  String get localBackupImport => 'استيراد الحسابات';

  @override
  String get localBackupImportDescription =>
      'استعادة حساباتك من ملف نسخة احتياطية تم إنشاؤه مسبقاً. اختر ملف JSON الذي يحتوي على بياناتك.';

  @override
  String get localBackupImportHint => 'الصق محتوى JSON هنا';

  @override
  String get localBackupImportButton => 'اختيار ملف';

  @override
  String get localBackupImportFilePrompt => 'اختر ملف JSON للاستيراد:';

  @override
  String get localBackupImportConfirmTitle => 'تأكيد الاستيراد';

  @override
  String get localBackupImportConfirmMessage =>
      'ستؤدي هذه العملية إلى إضافة الحسابات من ملف النسخة الاحتياطية إلى قائمتك الحالية. ستبقى حساباتك الموجودة محفوظة. هل تريد المتابعة؟';

  @override
  String localBackupImportSuccess(Object count) {
    return '✅ تم استيراد $count حساب بنجاح';
  }

  @override
  String get localBackupImportNoAccounts =>
      'ℹ️ لم يتم العثور على حسابات جديدة في الملف';

  @override
  String get localBackupImportError =>
      '❌ تعذر استيراد الحسابات. يرجى التحقق من صحة الملف.';

  @override
  String get localBackupNote =>
      'ملاحظة: لن يؤدي استيراد الحسابات إلى حذف حساباتك الحالية. ستتم إضافة الحسابات الجديدة إلى حساباتك الحالية.';

  @override
  String get verifyRipScannerTooltip => 'مسح رمز RIP';

  @override
  String get changeLanguageTooltip => 'تغيير اللغة';

  @override
  String get verifyRipHeaderTitle => 'التحقق من حساب RIP';

  @override
  String get verifyRipHeaderSubtitle => 'أدخل رمز RIP للتحقق من صلاحيته';

  @override
  String get verifyRipInputLabel => 'حساب RIP';

  @override
  String get verifyRipInputHint => 'التنسيق: 00799999xxxxxxxxxxxx';

  @override
  String get verifyRipPasteTooltip => 'لصق من الحافظة';

  @override
  String get verifyRipButtonText => 'تحقق';

  @override
  String get verifyRipValidationEmpty => 'الرجاء إدخال حساب RIP';

  @override
  String get verifyRipValidationLength =>
      'يجب أن يتكون رمز RIP من 20 رقمًا بالضبط';

  @override
  String get verifyRipValidationPrefix => 'يجب أن يبدأ رمز RIP بالرقم 00799999';

  @override
  String get verifyRipResultValid => 'حساب RIP صالح';

  @override
  String get verifyRipResultInvalid => 'حساب RIP غير صالح';

  @override
  String get verifyRipResultValidSub => 'تم التحقق من رمز RIP بنجاح';

  @override
  String get verifyRipResultInvalidSub => 'رمز RIP الذي تم إدخاله غير صحيح';

  @override
  String get verifyRipInfoTitle => 'معلومات الحساب';

  @override
  String get verifyRipInfoPrefixLabel => 'البادئة';

  @override
  String get verifyRipInfoBankCodeLabel => 'رمز البنك';

  @override
  String get verifyRipInfoCcpNumberLabel => 'رقم CCP';

  @override
  String get verifyRipInfoCcpKeyLabel => 'مفتاح CCP';

  @override
  String get verifyRipInfoRipKeyLabel => 'مفتاح RIP';

  @override
  String get verifyRipFullRipLabel => 'حساب RIP';

  @override
  String get verifyRipCopyButton => 'نسخ';

  @override
  String get verifyRipCopiedMessage => 'تم نسخ حساب RIP';

  @override
  String get verifyRipGenericInvalidMessage =>
      'حساب RIP الذي تم إدخاله غير صحيح. يرجى التحقق والمحاولة مرة أخرى.';

  @override
  String get changeLanguageDialogTitle => 'تغيير اللغة';

  @override
  String get ccpFormInputLabel => 'رقم CCP';

  @override
  String get ccpFormInputHint => 'أدخل رقم CCP';

  @override
  String get ccpFormValidationEmpty => 'الرجاء إدخال رقم CCP';

  @override
  String get ccpFormValidationInvalid => 'الرجاء إدخال رقم CCP صالح';

  @override
  String get ccpFormResultTitle => 'تم الحساب بنجاح';

  @override
  String get ccpFormResultSubtitle => 'رمز RIP الخاص بك جاهز للاستخدام';

  @override
  String get ccpFormCcpKeyLabel => 'مفتاح CCP';

  @override
  String get ccpFormRipKeyLabel => 'مفتاح RIP';

  @override
  String get ccpFormRipAccountLabel => 'حساب RIP';

  @override
  String get ccpFormSaveButton => 'حفظ الحساب';

  @override
  String get ccpFormSaveButtonShort => 'حفظ';

  @override
  String get ccpFormAccountExistsError => 'يوجد حساب بهذا الرمز RIP بالفعل';

  @override
  String get ccpFormClearTooltip => 'مسح';

  @override
  String get savedAccountsImportTooltip => 'استيراد الحسابات من ملف';

  @override
  String get savedAccountsExportTooltip => 'تصدير جميع حساباتي';

  @override
  String get savedAccountsSearchLabel => 'بحث';

  @override
  String get savedAccountsSearchHint => 'البحث بالاسم أو رقم CCP';

  @override
  String get savedAccountsEmptySearch => 'لم يتم العثور على حسابات';

  @override
  String get savedAccountsEmpty => 'لا توجد حسابات محفوظة';

  @override
  String get savedAccountsEditDialogTitle => 'تعديل الحساب';

  @override
  String get savedAccountsEditOwnerNameLabel => 'اسم المالك:';

  @override
  String get savedAccountsEditOwnerNameHint => 'أدخل الاسم';

  @override
  String get savedAccountsDialogCancel => 'إلغاء';

  @override
  String get savedAccountsDialogSave => 'حفظ';

  @override
  String savedAccountsImportSuccess(int count) {
    return 'تم استيراد $count حسابات بنجاح';
  }

  @override
  String savedAccountsImportError(String error) {
    return 'خطأ في استيراد الحسابات: $error';
  }

  @override
  String savedAccountsExportError(String error) {
    return 'خطأ في تصدير الحسابات: $error';
  }

  @override
  String get savedAccountsManageTitle => 'إدارة الحسابات';

  @override
  String get savedAccountsImportTitle => 'استيراد الحسابات';

  @override
  String get savedAccountsExportTitle => 'تصدير حساباتي';

  @override
  String get saveAccountScreenTitle => 'حفظ الحساب';

  @override
  String get saveAccountOwnerNameHint => 'أدخل اسم المالك';

  @override
  String get saveAccountOwnerNameValidation => 'الرجاء إدخال اسم المالك';

  @override
  String get saveAccountSuccessMessage => 'تم حفظ الحساب بنجاح';

  @override
  String get saveAccountGenericErrorPrefix => 'خطأ: ';

  @override
  String get savedAccountLastModified => 'تم التعديل في:';

  @override
  String get savedAccountCcpLabel => 'CCP:';

  @override
  String get savedAccountRipLabel => 'حساب RIP:';

  @override
  String get savedAccountCopyRip => 'نسخ RIP';

  @override
  String get savedAccountShare => 'مشاركة';

  @override
  String get savedAccountEdit => 'تعديل';

  @override
  String get savedAccountDelete => 'حذف';

  @override
  String get savedAccountRipCopied => 'تم نسخ حساب RIP';

  @override
  String get savedAccountDeleteTitle => 'حذف';

  @override
  String savedAccountDeleteConfirm(String ownerName) {
    return 'حذف حساب $ownerName؟';
  }

  @override
  String get savedAccountQrTitle => 'امسح كود QR';

  @override
  String get savedAccountQrClose => 'حسنا';

  @override
  String get savedAccountSaveButton => 'حفظ الحساب';

  @override
  String get verifyRipCopyButtonText => 'نسخ';

  @override
  String get verifyRipSaveButtonText => 'حفظ';

  @override
  String get ccpFormInputInstruction =>
      'أدخل رمز CCP بدون مفتاح (10 أرقام كحد أقصى)';

  @override
  String get appInfoButtonText => 'المزيد من المعلومات';

  @override
  String get appInfoScreenTitle => 'حول تطبيق CCP RIP DZ';

  @override
  String get appInfoContent =>
      '📄 حول تطبيق CCP RIP DZ\nمرحبًا بكم في تطبيق CCP RIP DZ!\nيوفر لكم هذا التطبيق وسيلة سهلة وفعالة لإدارة حسابات CCP في الجزائر. إليكم ما يمكنكم القيام به باستخدام هذا التطبيق:\n\n🔧 الميزات الرئيسية:\n\n✅ حساب رقم RIP: \nاحسب بسهولة رقم RIP (البيان البريدي) انطلاقًا من رقم CCP بدون المفتاح.\n\n💾 حفظ الحسابات: \nقم بحفظ الحسابات المُحتسبة للوصول إليها لاحقًا بسهولة.\n\n📤 مشاركة مبسطة:\n    - نسخ رقم RIP \n    - إنشاء رمز QR \n    - مشاركة الحساب عبر التطبيقات المختلفة \n\n🔍 التحقق من صحة RIP: \nتحقق من صحة رقم RIP موجود:\n\n     - عبر إدخال الرقم يدويًا \n     - أو عن طريق مسح رمز QR';

  @override
  String get ccpCalculationSuccess => 'تم الحساب بنجاح';

  @override
  String get ccpCalculationReady => 'رمز RIP الخاص بك جاهز للاستخدام';

  @override
  String get ccpKeyCcp => 'مفتاح CCP';

  @override
  String get ccpKeyRip => 'مفتاح RIP';

  @override
  String get ccpAccountRip => 'حساب RIP';

  @override
  String get ccpCopyButton => 'نسخ';

  @override
  String get ccpCopiedMessage => 'تم النسخ';

  @override
  String get ccpSaveButton => 'حفظ الحساب';

  @override
  String get updateAvailableTitle => 'تحديث متاح';

  @override
  String updateAvailableMessage(Object version) {
    return 'إصدار جديد ($version) من CCP RIP DZ متاح على متجر Google Play.';
  }

  @override
  String get updateRecommendation =>
      'نوصي بالتحديث للاستفادة من أحدث التحسينات والإصلاحات.';

  @override
  String get updateNow => 'تحديث الآن';

  @override
  String get updateLater => 'لاحقاً';

  @override
  String get updateNoUpdateAvailable => 'لديك بالفعل أحدث إصدار';
}
