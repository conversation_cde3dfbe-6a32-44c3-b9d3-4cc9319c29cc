{"@@locale": "en", "appTitle": "CCP RIP Calculator", "bottomNavCalculate": "Calculate", "bottomNavAccounts": "Accounts", "bottomNavVerify": "Verify", "homeScreenTitle": "CCP RIP Calculator", "savedAccountsScreenTitle": "Saved Accounts", "verifyRipScreenTitle": "Verify RIP Account", "menuPrivacyPolicy": "Privacy Policy", "menuRateApp": "Rate App", "menuUpdate": "Update", "menuAbout": "About", "menuQuit": "Quit", "menuCloudBackup": "Cloud Backup", "menuLocalBackup": "Local Backup", "cloudBackupTitle": "Cloud Backup", "cloudBackupInfo": "About Cloud Backup", "cloudBackupDescription": "Cloud backup allows you to save your CCP accounts to Google Drive. This way, you can restore your accounts if you change devices or reinstall the app.", "cloudBackupPrivacy": "Your data is stored privately in your Google Drive account. Only you have access to it.", "cloudBackupSignInRequired": "Sign in to Google Drive to use cloud backup", "cloudBackupSignIn": "Sign in with Google", "cloudBackupSignOut": "Sign out", "cloudBackupLastBackup": "Last backup", "cloudBackupBackup": "Backup", "cloudBackupRestore": "Rest<PERSON>", "cloudBackupSuccess": "Accounts backed up successfully", "cloudBackupRestoreSuccess": "Accounts restored successfully", "cloudBackupRestoreConfirmTitle": "Restore Accounts", "cloudBackupRestoreConfirmMessage": "This will replace your current accounts with the ones from your last backup. Continue?", "cancel": "Cancel", "confirm": "Confirm", "localBackupTitle": "Backup & Restore", "localBackupExport": "Export Your Accounts", "localBackupExportDescription": "Create a secure backup of all your CCP accounts in a file that you can safely store on your device or share.", "localBackupExportButton": "Export My Accounts", "localBackupExportSuccess": "✅ Your accounts have been exported successfully", "localBackupExportError": "❌ Unable to export your accounts. Please try again.", "localBackupImport": "Import Accounts", "localBackupImportDescription": "Restore your accounts from a previously created backup file. Select the JSON file containing your data.", "localBackupImportHint": "Paste JSON content here", "localBackupImportButton": "Select File", "localBackupImportFilePrompt": "Select a JSON file to import:", "localBackupImportConfirmTitle": "Confirm Import", "localBackupImportConfirmMessage": "This action will add the accounts from the backup file to your current list. Your existing accounts will be preserved. Do you want to continue?", "localBackupImportSuccess": "✅ {count} account(s) imported successfully", "localBackupImportNoAccounts": "ℹ️ No new accounts found in the file", "localBackupImportError": "❌ Unable to import accounts. Please verify the file is valid.", "localBackupNote": "Note: Importing accounts will not delete your existing accounts. New accounts will be added to your existing ones.", "verifyRipScannerTooltip": "Scan RIP Code", "changeLanguageTooltip": "Change Language", "verifyRipHeaderTitle": "RIP Account Verification", "verifyRipHeaderSubtitle": "Enter an RIP code to check its validity", "verifyRipInputLabel": "RIP Account", "verifyRipInputHint": "Format: ********xxxxxxxxxxxx", "verifyRipPasteTooltip": "Paste from clipboard", "verifyRipButtonText": "VERIFY", "verifyRipValidationEmpty": "Please enter an RIP account", "verifyRipValidationLength": "RIP code must be exactly 20 digits", "verifyRipValidationPrefix": "RIP code must start with ********", "verifyRipResultValid": "<PERSON>id <PERSON> Account", "verifyRipResultInvalid": "Invalid RIP Account", "verifyRipResultValidSub": "The RIP code was successfully verified", "verifyRipResultInvalidSub": "The entered RIP code is incorrect", "verifyRipInfoTitle": "Account Information", "verifyRipInfoPrefixLabel": "Prefix", "verifyRipInfoBankCodeLabel": "Bank Code", "verifyRipInfoCcpNumberLabel": "CCP Number", "verifyRipInfoCcpKeyLabel": "CCP Key", "verifyRipInfoRipKeyLabel": "RIP Key", "verifyRipFullRipLabel": "RIP Account", "verifyRipCopyButton": "Copy", "verifyRipCopiedMessage": "RIP Account copied", "verifyRipGenericInvalidMessage": "The entered RIP account is incorrect. Please check and try again.", "changeLanguageDialogTitle": "Change Language", "ccpFormInputLabel": "CCP Number", "ccpFormInputHint": "Enter CCP number", "ccpFormValidationEmpty": "Please enter a CCP number", "ccpFormValidationInvalid": "Please enter a valid CCP number", "ccpFormResultTitle": "Calculation successful", "ccpFormResultSubtitle": "Your RIP code is ready to use", "ccpFormCcpKeyLabel": "CCP Key", "ccpFormRipKeyLabel": "RIP Key", "ccpFormRipAccountLabel": "RIP Account", "ccpFormSaveButton": "SAVE ACCOUNT", "ccpFormSaveButtonShort": "SAVE", "ccpFormAccountExistsError": "An account with this RIP code already exists", "ccpFormClearTooltip": "Clear", "savedAccountsImportTooltip": "Import accounts from file", "savedAccountsExportTooltip": "Export all my accounts", "savedAccountsSearchLabel": "Search", "savedAccountsSearchHint": "Search by name or CCP number", "savedAccountsEmptySearch": "No accounts found", "savedAccountsEmpty": "No saved accounts", "savedAccountsEditDialogTitle": "Edit Account", "savedAccountsEditOwnerNameLabel": "Owner's name:", "savedAccountsEditOwnerNameHint": "Enter name", "savedAccountsDialogCancel": "CANCEL", "savedAccountsDialogSave": "SAVE", "savedAccountsImportSuccess": "{count} accounts imported successfully", "@savedAccountsImportSuccess": {"description": "Success message for importing accounts", "placeholders": {"count": {"type": "int", "example": "5"}}}, "savedAccountsImportError": "Error importing accounts: {error}", "@savedAccountsImportError": {"description": "Error message for importing accounts", "placeholders": {"error": {"type": "String", "example": "File format error"}}}, "savedAccountsExportError": "Error exporting accounts: {error}", "@savedAccountsExportError": {"description": "Error message for exporting accounts", "placeholders": {"error": {"type": "String", "example": "Permission denied"}}}, "savedAccountsManageTitle": "Account Management", "@savedAccountsManageTitle": {"description": "Title for the manage accounts dialog"}, "savedAccountsImportTitle": "Import Accounts", "@savedAccountsImportTitle": {"description": "Title for the import option in manage accounts dialog"}, "savedAccountsExportTitle": "Export My Accounts", "@savedAccountsExportTitle": {"description": "Title for the export option in manage accounts dialog"}, "saveAccountScreenTitle": "Save Account", "saveAccountOwnerNameHint": "Enter owner's name", "saveAccountOwnerNameValidation": "Please enter the owner's name", "saveAccountSuccessMessage": "Account saved successfully", "saveAccountGenericErrorPrefix": "Error: ", "savedAccountLastModified": "Modified on:", "savedAccountCcpLabel": "CCP:", "savedAccountRipLabel": "RIP Account:", "savedAccountCopyRip": "Copy RIP", "savedAccountShare": "Share", "savedAccountEdit": "Edit", "savedAccountDelete": "Delete", "savedAccountRipCopied": "RIP Account copied", "savedAccountDeleteTitle": "Delete", "savedAccountDeleteConfirm": "Delete {owner<PERSON><PERSON>}'s account?", "@savedAccountDeleteConfirm": {"description": "Confirmation message for deleting an account", "placeholders": {"ownerName": {"type": "String", "example": "<PERSON>"}}}, "savedAccountQrTitle": "Scan QR Code", "savedAccountQrClose": "Close", "savedAccountSaveButton": "Save Account", "verifyRipCopyButtonText": "Copy", "verifyRipSaveButtonText": "Save", "ccpFormInputInstruction": "Enter a CCP code without key (maximum 10 digits)", "appInfoButtonText": "More information", "appInfoScreenTitle": "About CCP RIP DZ", "appInfoContent": "📄 About CCP RIP DZ\nWelcome to the CCP RIP DZ app!\nOur app provides a simple and effective solution for managing CCP accounts in Algeria. Here's what you can do with it:\n\n🔧 Key Features:\n\n✅ RIP Calculation: \nEasily calculate the RIP (Postal Identity Statement) from your CCP number without the key.\n\n💾 Save Accounts: \nStore calculated accounts for quick access later.\n\n📤 Easy Sharing:\n    - Copy the RIP code \n    - Generate a QR Code \n    - Share the account through your favorite apps \n\n🔍 RIP Validation: \nCheck the validity of an existing RIP code:\n\n     - By entering it manually \n     - Or scanning a QR Code", "ccpCalculationSuccess": "Calculation completed successfully", "ccpCalculationReady": "Your RIP code is ready to use", "ccpKeyCcp": "CCP Key", "ccpKeyRip": "RIP Key", "ccpAccountRip": "RIP Account", "ccpCopyButton": "Copy", "ccpCopiedMessage": "<PERSON>pied", "ccpSaveButton": "SAVE ACCOUNT"}