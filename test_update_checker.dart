import 'package:flutter/material.dart';
import 'lib/services/update_checker_service.dart';
import 'lib/l10n/app_localizations.dart';

/// Fichier de test pour vérifier le système de détection des mises à jour
/// 
/// Ce fichier permet de tester manuellement le système de vérification des mises à jour
/// sans avoir à attendre le démarrage automatique de l'application.

void main() {
  runApp(const UpdateCheckerTestApp());
}

class UpdateCheckerTestApp extends StatelessWidget {
  const UpdateCheckerTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Update Checker',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const UpdateCheckerTestScreen(),
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
    );
  }
}

class UpdateCheckerTestScreen extends StatefulWidget {
  const UpdateCheckerTestScreen({super.key});

  @override
  State<UpdateCheckerTestScreen> createState() => _UpdateCheckerTestScreenState();
}

class _UpdateCheckerTestScreenState extends State<UpdateCheckerTestScreen> {
  final UpdateCheckerService _updateChecker = UpdateCheckerService();
  String _status = 'Prêt pour le test';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Update Checker'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🧪 Test du système de mise à jour',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Ce test simule une version plus récente (2.1.3) pour déclencher le dialog de mise à jour.',
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Statut: $_status',
                      style: TextStyle(
                        color: _isLoading ? Colors.orange : Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testUpdateChecker,
              icon: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.system_update),
              label: Text(_isLoading ? 'Vérification...' : 'Tester la vérification de mise à jour'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testManualCheck,
              icon: const Icon(Icons.refresh),
              label: const Text('Test vérification manuelle'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 20),
            const Card(
              color: Colors.blue,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📋 Instructions de test:',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. Cliquez sur "Tester la vérification de mise à jour"\n'
                      '2. Un dialog devrait apparaître avec la version 2.1.3\n'
                      '3. Testez les boutons "Plus tard" et "Mettre à jour"\n'
                      '4. Le bouton "Mettre à jour" devrait ouvrir le Play Store',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testUpdateChecker() async {
    setState(() {
      _isLoading = true;
      _status = 'Vérification en cours...';
    });

    try {
      // Simuler un délai de vérification
      await Future.delayed(const Duration(seconds: 1));
      
      final hasUpdate = await _updateChecker.checkForUpdate();
      
      if (hasUpdate && mounted) {
        final latestVersion = await _updateChecker.getLatestVersionFromPlayStore();
        if (latestVersion != null && mounted) {
          _updateChecker.showUpdateDialog(context, latestVersion);
          setState(() {
            _status = 'Dialog de mise à jour affiché (version $latestVersion)';
          });
        }
      } else {
        setState(() {
          _status = 'Aucune mise à jour disponible';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Erreur: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testManualCheck() async {
    setState(() {
      _isLoading = true;
      _status = 'Vérification manuelle...';
    });

    try {
      final hasUpdate = await _updateChecker.checkForUpdate();
      
      if (hasUpdate && mounted) {
        final latestVersion = await _updateChecker.getLatestVersionFromPlayStore();
        if (latestVersion != null && mounted) {
          _updateChecker.showUpdateDialog(context, latestVersion);
          setState(() {
            _status = 'Mise à jour disponible: $latestVersion';
          });
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Vous avez déjà la dernière version'),
              backgroundColor: Colors.green,
            ),
          );
          setState(() {
            _status = 'Aucune mise à jour disponible';
          });
        }
      }
    } catch (e) {
      setState(() {
        _status = 'Erreur: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
