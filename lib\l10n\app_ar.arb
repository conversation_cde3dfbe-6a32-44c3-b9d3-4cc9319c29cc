{"@@locale": "ar", "appTitle": "حاسبة CCP RIP", "bottomNavCalculate": "ح<PERSON><PERSON><PERSON>", "bottomNavAccounts": "الحسابات", "bottomNavVerify": "تحقق", "homeScreenTitle": "حاسبة CCP RIP", "savedAccountsScreenTitle": "الحسابات المحفوظة", "verifyRipScreenTitle": "التحقق من حساب RIP", "menuPrivacyPolicy": "سياسة الخصوصية", "menuRateApp": "تقييم التطبيق", "menuUpdate": "تحديث", "menuAbout": "حو<PERSON>", "menuQuit": "خروج", "menuCloudBackup": "النسخ الاحتياطي السحابي", "menuLocalBackup": "النسخ الاحتياطي المحلي", "cloudBackupTitle": "النسخ الاحتياطي السحابي", "cloudBackupInfo": "حول النسخ الاحتياطي السحابي", "cloudBackupDescription": "يتيح لك النسخ الاحتياطي السحابي حفظ حسابات CCP الخاصة بك على Google Drive. بهذه الطريقة، يمكنك استعادة حساباتك إذا قمت بتغيير الأجهزة أو إعادة تثبيت التطبيق.", "cloudBackupPrivacy": "يتم تخزين بياناتك بشكل خاص في حساب Google Drive الخاص بك. أنت فقط من يمكنه الوصول إليها.", "cloudBackupSignInRequired": "قم بتسجيل الدخول إلى Google Drive لاستخدام النسخ الاحتياطي السحابي", "cloudBackupSignIn": "تسجيل الدخول باستخدام Google", "cloudBackupSignOut": "تسجيل الخروج", "cloudBackupLastBackup": "آخر نسخة احتياطية", "cloudBackupBackup": "نسخ احتياطي", "cloudBackupRestore": "استعادة", "cloudBackupSuccess": "تم حفظ الحسابات بنجاح", "cloudBackupRestoreSuccess": "تمت استعادة الحسابات بنجاح", "cloudBackupRestoreConfirmTitle": "استعادة الحسابات", "cloudBackupRestoreConfirmMessage": "سيؤدي هذا إلى استبدال حساباتك الحالية بتلك الموجودة في النسخة الاحتياطية الأخيرة. هل تريد المتابعة؟", "cancel": "إلغاء", "confirm": "تأكيد", "localBackupTitle": "النسخ الاحتياطي والاستعادة", "localBackupExport": "تصدير حساباتك", "localBackupExportDescription": "إنشاء نسخة احتياطية آمنة لجميع حسابات CCP الخاصة بك في ملف يمكنك حفظه بأمان على جهازك أو مشاركته.", "localBackupExportButton": "تصدير حساباتي", "localBackupExportSuccess": "✅ تم تصدير حساباتك بنجاح", "localBackupExportError": "❌ تعذر تصدير حساباتك. يرجى المحاولة مرة أخرى.", "localBackupImport": "استيراد الحسابات", "localBackupImportDescription": "استعادة حساباتك من ملف نسخة احتياطية تم إنشاؤه مسبقاً. اختر ملف JSON الذي يحتوي على بياناتك.", "localBackupImportHint": "الصق محتوى JSON هنا", "localBackupImportButton": "اختيار ملف", "localBackupImportConfirmTitle": "تأكيد الاستيراد", "localBackupImportConfirmMessage": "ستؤدي هذه العملية إلى إضافة الحسابات من ملف النسخة الاحتياطية إلى قائمتك الحالية. ستبقى حساباتك الموجودة محفوظة. هل تريد المتابعة؟", "localBackupImportSuccess": "✅ تم استيراد {count} حساب بنجاح", "localBackupImportNoAccounts": "ℹ️ لم يتم العثور على حسابات جديدة في الملف", "localBackupImportError": "❌ تعذر استيراد الحسابات. يرجى التحقق من صحة الملف.", "localBackupNote": "ملاحظة: لن يؤدي استيراد الحسابات إلى حذف حساباتك الحالية. ستتم إضافة الحسابات الجديدة إلى حساباتك الحالية.", "verifyRipScannerTooltip": "م<PERSON><PERSON> <PERSON><PERSON>ز RIP", "changeLanguageTooltip": "تغيير اللغة", "verifyRipHeaderTitle": "التحقق من حساب RIP", "verifyRipHeaderSubtitle": "أدخل رمز RIP للتحقق من صلاحيته", "verifyRipInputLabel": "حساب RIP", "verifyRipInputHint": "التنسيق: ********xxxxxxxxxxxx", "verifyRipPasteTooltip": "لصق من الحافظة", "verifyRipButtonText": "تحقق", "verifyRipValidationEmpty": "الرجاء إدخال حساب RIP", "verifyRipValidationLength": "يجب أن يتكون رمز RIP من 20 رقمًا بالضبط", "verifyRipValidationPrefix": "يج<PERSON> أن يبدأ رمز RIP بالرقم ********", "verifyRipResultValid": "حساب RIP صالح", "verifyRipResultInvalid": "حساب RIP غير صالح", "verifyRipResultValidSub": "تم التحقق من رمز RIP بنجاح", "verifyRipResultInvalidSub": "رمز RIP الذي تم إدخاله غير صحيح", "verifyRipInfoTitle": "معلومات الحساب", "verifyRipInfoPrefixLabel": "البادئة", "verifyRipInfoBankCodeLabel": "<PERSON><PERSON><PERSON> البنك", "verifyRipInfoCcpNumberLabel": "رقم CCP", "verifyRipInfoCcpKeyLabel": "مفتاح CCP", "verifyRipInfoRipKeyLabel": "مفتاح RIP", "verifyRipFullRipLabel": "حساب RIP", "verifyRipCopyButton": "نسخ", "verifyRipCopiedMessage": "تم نسخ حساب RIP", "verifyRipGenericInvalidMessage": "حساب RIP الذي تم إدخاله غير صحيح. يرجى التحقق والمحاولة مرة أخرى.", "changeLanguageDialogTitle": "تغيير اللغة", "ccpFormInputLabel": "رقم CCP", "ccpFormInputHint": "أدخل رقم CCP", "ccpFormValidationEmpty": "الرجاء إدخال رقم CCP", "ccpFormValidationInvalid": "الرجاء إدخال رقم CCP صالح", "ccpFormResultTitle": "تم الحساب بنجاح", "ccpFormResultSubtitle": "رمز RIP الخاص بك جاهز للاستخدام", "ccpFormCcpKeyLabel": "مفتاح CCP", "ccpFormRipKeyLabel": "مفتاح RIP", "ccpFormRipAccountLabel": "حساب RIP", "ccpFormSaveButton": "<PERSON><PERSON><PERSON> الحساب", "ccpFormSaveButtonShort": "<PERSON><PERSON><PERSON>", "ccpFormAccountExistsError": "يوجد حساب بهذا الرمز RIP بالفعل", "ccpFormClearTooltip": "م<PERSON><PERSON>", "savedAccountsImportTooltip": "استيراد الحسابات من ملف", "savedAccountsExportTooltip": "تصدير جميع حساباتي", "savedAccountsSearchLabel": "ب<PERSON><PERSON>", "savedAccountsSearchHint": "البحث بالاسم أو رقم CCP", "savedAccountsEmptySearch": "لم يتم العثور على حسابات", "savedAccountsEmpty": "لا توجد حسابات محفوظة", "savedAccountsEditDialogTitle": "تعديل الحساب", "savedAccountsEditOwnerNameLabel": "اسم المالك:", "savedAccountsEditOwnerNameHint": "أ<PERSON><PERSON><PERSON> الاسم", "savedAccountsDialogCancel": "إلغاء", "savedAccountsDialogSave": "<PERSON><PERSON><PERSON>", "savedAccountsImportSuccess": "تم استيراد {count} حسابات بنجاح", "@savedAccountsImportSuccess": {"description": "رسالة نجاح لاستيراد الحسابات", "placeholders": {"count": {"type": "int", "example": "5"}}}, "savedAccountsImportError": "خطأ في استيراد الحسابات: {error}", "@savedAccountsImportError": {"description": "رسالة خطأ لاستيراد الحسابات", "placeholders": {"error": {"type": "String", "example": "خطأ في تنسيق الملف"}}}, "savedAccountsExportError": "خطأ في تصدير الحسابات: {error}", "@savedAccountsExportError": {"description": "رسالة خطأ لتصدير الحسابات", "placeholders": {"error": {"type": "String", "example": "تم رفض الإذن"}}}, "savedAccountsManageTitle": "إدارة الحسابات", "savedAccountsImportTitle": "استيراد الحسابات", "savedAccountsExportTitle": "تصدير حساباتي", "saveAccountScreenTitle": "<PERSON><PERSON><PERSON> الحساب", "saveAccountOwnerNameHint": "أد<PERSON>ل اسم المالك", "saveAccountOwnerNameValidation": "الرجاء إدخال اسم المالك", "saveAccountSuccessMessage": "تم حفظ الحساب بنجاح", "saveAccountGenericErrorPrefix": "خطأ: ", "savedAccountLastModified": "تم التعديل في:", "savedAccountCcpLabel": "CCP:", "savedAccountRipLabel": "حساب RIP:", "savedAccountCopyRip": "نسخ RIP", "savedAccountShare": "مشاركة", "savedAccountEdit": "تعديل", "savedAccountDelete": "<PERSON><PERSON><PERSON>", "savedAccountRipCopied": "تم نسخ حساب RIP", "savedAccountDeleteTitle": "<PERSON><PERSON><PERSON>", "savedAccountDeleteConfirm": "حذف حساب {ownerName}؟", "@savedAccountDeleteConfirm": {"description": "رسالة تأكيد لحذف حساب", "placeholders": {"ownerName": {"type": "String", "example": "<PERSON>"}}}, "savedAccountQrTitle": "ا<PERSON><PERSON><PERSON> QR", "savedAccountQrClose": "حسنا", "savedAccountSaveButton": "<PERSON><PERSON><PERSON> الحساب", "verifyRipCopyButtonText": "نسخ", "verifyRipSaveButtonText": "<PERSON><PERSON><PERSON>", "ccpFormInputInstruction": "أدخل رمز CCP بدون مفتاح (10 أرقام كحد أقصى)", "appInfoButtonText": "المزيد من المعلومات", "appInfoScreenTitle": "حول تطبيق CCP RIP DZ", "appInfoContent": "📄 حول تطبيق CCP RIP DZ\nمرحبًا بكم في تطبيق CCP RIP DZ!\nيوفر لكم هذا التطبيق وسيلة سهلة وفعالة لإدارة حسابات CCP في الجزائر. إليكم ما يمكنكم القيام به باستخدام هذا التطبيق:\n\n🔧 الميزات الرئيسية:\n\n✅ حساب رقم RIP: \nاحسب بسهولة رقم RIP (البيان البريدي) انطلاقًا من رقم CCP بدون المفتاح.\n\n💾 حفظ الحسابات: \nقم بحفظ الحسابات المُحتسبة للوصول إليها لاحقًا بسهولة.\n\n📤 مشاركة مبسطة:\n    - نسخ رقم RIP \n    - إنشاء رمز QR \n    - مشاركة الحساب عبر التطبيقات المختلفة \n\n🔍 التحقق من صحة RIP: \nتحقق من صحة رقم RIP موجود:\n\n     - عبر إدخال الرقم يدويًا \n     - أو عن طريق مسح رمز QR", "ccpCalculationSuccess": "تم الحساب بنجاح", "ccpCalculationReady": "رمز RIP الخاص بك جاهز للاستخدام", "ccpKeyCcp": "مفتاح CCP", "ccpKeyRip": "مفتاح RIP", "ccpAccountRip": "حساب RIP", "ccpCopyButton": "نسخ", "ccpCopiedMessage": "تم النسخ", "ccpSaveButton": "<PERSON><PERSON><PERSON> الحساب"}