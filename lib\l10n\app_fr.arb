{"@@locale": "fr", "appTitle": "Calculateur CCP RIP", "bottomNavCalculate": "Calculer", "bottomNavAccounts": "<PERSON><PERSON><PERSON>", "bottomNavVerify": "Vérifier", "homeScreenTitle": "Calculateur CCP RIP", "savedAccountsScreenTitle": "Comptes Enregistrés", "verifyRipScreenTitle": "Vérifier Compte RIP", "menuPrivacyPolicy": "Politique de confidentialité", "menuRateApp": "Noter l'application", "menuUpdate": "Mise à jour", "menuAbout": "À propos", "menuQuit": "<PERSON><PERSON><PERSON>", "menuCloudBackup": "Sauvegarde Cloud", "menuLocalBackup": "Sauvegarde Locale", "cloudBackupTitle": "Sauvegarde Cloud", "cloudBackupInfo": "À propos de la sauvegarde cloud", "cloudBackupDescription": "La sauvegarde cloud vous permet d'enregistrer vos comptes CCP sur Google Drive. Ainsi, vous pouvez restaurer vos comptes si vous changez d'appareil ou réinstallez l'application.", "cloudBackupPrivacy": "Vos données sont stockées de manière privée dans votre compte Google Drive. Vous seul y avez accès.", "cloudBackupSignInRequired": "Connectez-vous à Google Drive pour utiliser la sauvegarde cloud", "cloudBackupSignIn": "Se connecter avec Google", "cloudBackupSignOut": "Se déconnecter", "cloudBackupLastBackup": "<PERSON><PERSON><PERSON> sauve<PERSON>", "cloudBackupBackup": "<PERSON><PERSON><PERSON><PERSON>", "cloudBackupRestore": "<PERSON><PERSON><PERSON>", "cloudBackupSuccess": "Comptes sauvegardés avec succès", "cloudBackupRestoreSuccess": "Comptes restaurés avec succès", "cloudBackupRestoreConfirmTitle": "Restaurer les comptes", "cloudBackupRestoreConfirmMessage": "<PERSON><PERSON> remplacera vos comptes actuels par ceux de votre dernière sauvegarde. Continuer ?", "cancel": "Annuler", "confirm": "Confirmer", "localBackupTitle": "Sauvegarde et Restauration", "localBackupExport": "Exporter vos comptes", "localBackupExportDescription": "C<PERSON>ez une sauvegarde sécurisée de tous vos comptes CCP dans un fichier que vous pouvez conserver sur votre appareil ou partager en toute sécurité.", "localBackupExportButton": "Exporter mes comptes", "localBackupExportSuccess": "✅ Vos comptes ont été exportés avec succès", "localBackupExportError": "❌ Impossible d'exporter vos comptes. Veuillez réessayer.", "localBackupImport": "Importer des comptes", "localBackupImportDescription": "Restaurez vos comptes à partir d'un fichier de sauvegarde précédemment créé. Sélectionnez le fichier JSON contenant vos données.", "localBackupImportHint": "Collez le contenu JSON ici", "localBackupImportButton": "Sélectionner un fichier", "localBackupImportFilePrompt": "Sélectionnez un fichier JSON à importer :", "localBackupImportConfirmTitle": "Confirmer l'importation", "localBackupImportConfirmMessage": "Cette action ajoutera les comptes du fichier de sauvegarde à votre liste actuelle. Vos comptes existants seront conservés. Souhaitez-vous continuer ?", "localBackupImportSuccess": "✅ {count} compte(s) importé(s) avec succès", "localBackupImportNoAccounts": "ℹ️ Aucun nouveau compte trouvé dans le fichier", "localBackupImportError": "❌ Impossible d'importer les comptes. Vérifiez que le fichier est valide.", "localBackupNote": "Remarque : L'importation de comptes ne supprimera pas vos comptes existants. Les nouveaux comptes seront ajoutés à vos comptes existants.", "verifyRipScannerTooltip": "Scanner Code RIP", "changeLanguageTooltip": "Changer de langue", "verifyRipHeaderTitle": "Vérification de Compte RIP", "verifyRipHeaderSubtitle": "Entrez un code RIP pour vérifier sa validité", "verifyRipInputLabel": "Compte RIP", "verifyRipInputHint": "Format: ********xxxxxxxxxxxx", "verifyRipPasteTooltip": "Coller depuis le presse-papiers", "verifyRipButtonText": "VÉRIFIER", "verifyRipValidationEmpty": "Veuillez entrer un compte RIP", "verifyRipValidationLength": "Le code RIP doit contenir exactement 20 chiffres", "verifyRipValidationPrefix": "Le code RIP doit commencer par ********", "verifyRipResultValid": "Compte RIP valide", "verifyRipResultInvalid": "Compte RIP invalide", "verifyRipResultValidSub": "Le code RIP a été vérifié avec succès", "verifyRipResultInvalidSub": "Le code RIP saisi est incorrect", "verifyRipInfoTitle": "Informations du compte", "verifyRipInfoPrefixLabel": "Préfixe", "verifyRipInfoBankCodeLabel": "Code Banque", "verifyRipInfoCcpNumberLabel": "Numéro CCP", "verifyRipInfoCcpKeyLabel": "Clé CCP", "verifyRipInfoRipKeyLabel": "Clé RIP", "verifyRipFullRipLabel": "Compte RIP", "verifyRipCopyButton": "<PERSON><PERSON><PERSON>", "verifyRipCopiedMessage": "Compte RIP copié", "verifyRipGenericInvalidMessage": "Le compte RIP saisi est incorrect. Veuillez vérifier et réessayer.", "changeLanguageDialogTitle": "Changer de langue", "ccpFormInputLabel": "Numéro CCP", "ccpFormInputHint": "Entrez le numéro CCP", "ccpFormInputInstruction": "Saisissez un code CCP sans clé (10 chiffres maximum)", "ccpFormMoreInfo": "Plus d'informations", "ccpFormValidationEmpty": "Veuillez entrer un numéro CCP", "ccpFormValidationInvalid": "Veuillez entrer un numéro CCP valide", "ccpFormResultTitle": "Calcul effectué avec succès", "ccpFormResultSubtitle": "Votre code RIP est prêt à être utilisé", "ccpFormCcpKeyLabel": "Clé CCP", "ccpFormRipKeyLabel": "Clé RIP", "ccpFormRipAccountLabel": "Compte RIP", "ccpFormSaveButton": "ENREGISTRER LE COMPTE", "ccpFormSaveButtonShort": "ENREGISTRER", "ccpFormAccountExistsError": "Un compte avec ce code RIP existe déjà", "ccpFormClearTooltip": "<PERSON><PERSON><PERSON><PERSON>", "savedAccountsImportTooltip": "Importer des comptes depuis un fichier", "savedAccountsExportTooltip": "Exporter tous mes comptes", "savedAccountsSearchLabel": "<PERSON><PERSON><PERSON>", "savedAccountsSearchHint": "Rechercher par nom ou numéro CCP", "savedAccountsEmptySearch": "Aucun compte trouvé", "savedAccountsEmpty": "Aucun compte enregistré", "savedAccountsEditDialogTitle": "Modifier le Compte", "savedAccountsEditOwnerNameLabel": "Nom du propriétaire :", "savedAccountsEditOwnerNameHint": "Entrez le nom", "savedAccountsDialogCancel": "ANNULER", "savedAccountsDialogSave": "ENREGISTRER", "savedAccountsImportSuccess": "{count} comptes importés avec succès", "@savedAccountsImportSuccess": {"description": "Message de succès pour l'importation de comptes", "placeholders": {"count": {"type": "int", "example": "5"}}}, "savedAccountsImportError": "Erreur lors de l'importation des comptes: {error}", "@savedAccountsImportError": {"description": "Message d'erreur pour l'importation de comptes", "placeholders": {"error": {"type": "String", "example": "Erreur de format de fichier"}}}, "savedAccountsExportError": "Erreur lors de l'exportation des comptes: {error}", "@savedAccountsExportError": {"description": "Message d'erreur pour l'exportation de comptes", "placeholders": {"error": {"type": "String", "example": "Permission refusée"}}}, "savedAccountsManageTitle": "Gestion des comptes", "savedAccountsImportTitle": "Importer des comptes", "savedAccountsExportTitle": "Exporter mes comptes", "saveAccountScreenTitle": "Enregis<PERSON><PERSON> le Compte", "saveAccountOwnerNameHint": "Entrez le nom du propriétaire", "saveAccountOwnerNameValidation": "Veuillez entrer le nom du propriétaire", "saveAccountSuccessMessage": "Compte enregistré avec succès", "saveAccountGenericErrorPrefix": "Erreur: ", "savedAccountLastModified": "Modi<PERSON><PERSON> le:", "savedAccountCcpLabel": "CCP:", "savedAccountRipLabel": "Compte RIP:", "savedAccountCopyRip": "Copier RIP", "savedAccountShare": "Partager", "savedAccountEdit": "Modifier", "savedAccountDelete": "<PERSON><PERSON><PERSON><PERSON>", "savedAccountRipCopied": "Compte RIP copié", "savedAccountDeleteTitle": "<PERSON><PERSON><PERSON><PERSON>", "savedAccountDeleteConfirm": "Supprimer le compte de {ownerName} ?", "@savedAccountDeleteConfirm": {"description": "Message de confirmation pour la suppression d'un compte", "placeholders": {"ownerName": {"type": "String", "example": "<PERSON>"}}}, "savedAccountQrTitle": "Scan code QR", "savedAccountQrClose": "<PERSON><PERSON><PERSON>", "savedAccountSaveButton": "Enregistrer le compte", "verifyRipCopyButtonText": "<PERSON><PERSON><PERSON>", "verifyRipSaveButtonText": "Sauver", "appInfoButtonText": "Plus d'informations", "appInfoScreenTitle": "À propos de CCP RIP DZ", "appInfoContent": "📄 À propos de CCP RIP DZ\nBienvenue sur notre application CCP RIP DZ !\nNotre application vous offre une solution simple et efficace pour gérer vos comptes CCP en Algérie. Voici ce que vous pouvez faire avec notre outil :\n\n🔧 Fonctionnalités principales :\n\n✅ Calcul du RIP : \nCalculez facilement le RIP (Relevé d'Identité Postale) à partir de votre numéro CCP sans clé.\n\n💾 Sauvegarde des comptes : \nEnregistrez les comptes calculés pour un accès rapide.\n\n📤 Partage simplifié :\n    - Copier le code RIP \n    - Générer un Code QR \n    - Partager le compte via vos applications préférées \n\n🔍 Vérification du RIP : \nVérifiez la validité d'un RIP existant :\n\n     - En saisissant manuellement le code RIP \n     - Ou en scannant un QR Code", "ccpCalculationSuccess": "Calcul effectué avec succès", "ccpCalculationReady": "Votre code RIP est prêt à être utilisé", "ccpKeyCcp": "Clé CCP", "ccpKeyRip": "Clé RIP", "ccpAccountRip": "Compte RIP", "ccpCopyButton": "<PERSON><PERSON><PERSON>", "ccpCopiedMessage": "<PERSON><PERSON><PERSON>", "ccpSaveButton": "ENREGISTRER LE COMPTE", "updateAvailableTitle": "Mise à jour disponible", "updateAvailableMessage": "Une nouvelle version ({version}) de CCP RIP DZ est disponible sur Google Play Store.", "updateRecommendation": "Nous recommandons de mettre à jour pour bénéficier des dernières améliorations et corrections.", "updateNow": "Mettre à jour", "updateLater": "Plus tard", "updateNoUpdateAvailable": "Vous avez déjà la dernière version"}