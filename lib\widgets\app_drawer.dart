import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../l10n/app_localizations.dart';


import '../screens/local_backup_screen.dart';
import '../screens/main_screen.dart';
import '../screens/privacy_policy_screen.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  String _version = '';

  @override
  void initState() {
    super.initState();
    _loadVersion();
  }

  Future<void> _loadVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = 'Version ${packageInfo.version}';
      });
    } catch (e) {
      setState(() {
        _version = 'Version 2.1.0';
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);

    return Drawer(
      child: Column(
        children: [
          // En-tête du drawer avec logo et nom de l'app
          Container(
            width: double.infinity,
            padding: const EdgeInsets.only(top: 40, bottom: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withAlpha(180),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/logo2.png',
                  width: 50,
                  height: 50,
                ),
                const SizedBox(width: 12),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'CCP RIP DZ',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _version.isEmpty ? 'Version 2.1.1' : _version,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        letterSpacing: 0.3,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Options du menu
          Expanded(
            child: ListView(
              padding: const EdgeInsets.only(top: 12),
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.privacy_tip,
                  title: loc.menuPrivacyPolicy,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
                    );
                  },
                ),

                _buildDrawerItem(
                  context,
                  icon: Icons.save_alt,
                  title: loc.menuLocalBackup,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const LocalBackupScreen()),
                    );
                  },
                ),
                const Divider(height: 16, thickness: 0.5, indent: 8, endIndent: 8),
                _buildDrawerItem(
                  context,
                  icon: Icons.star,
                  title: loc.menuRateApp,
                  onTap: () {
                    Navigator.pop(context);
                    handleAppMenuChoice(context, AppMenuChoice.rate);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.system_update,
                  title: loc.menuUpdate,
                  onTap: () {
                    Navigator.pop(context);
                    handleAppMenuChoice(context, AppMenuChoice.update);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.info,
                  title: loc.menuAbout,
                  onTap: () {
                    Navigator.pop(context);
                    handleAppMenuChoice(context, AppMenuChoice.about);
                  },
                ),
                const Divider(height: 16, thickness: 0.5, indent: 8, endIndent: 8),
                _buildDrawerItem(
                  context,
                  icon: Icons.exit_to_app,
                  title: loc.menuQuit,
                  onTap: () {
                    Navigator.pop(context);
                    SystemNavigator.pop();
                  },
                  color: Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour construire un élément du drawer
  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    final isRTL = Localizations.localeOf(context).languageCode == 'ar';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(6),
        child: InkWell(
          borderRadius: BorderRadius.circular(6),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: (color ?? Theme.of(context).primaryColor).withAlpha(15),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    icon,
                    color: color ?? Theme.of(context).primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 16), // Augmenté de 10 à 16 pour plus d'espacement
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: color ?? Colors.black87,
                    ),
                    textAlign: isRTL ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
