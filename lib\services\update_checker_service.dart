import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../l10n/app_localizations.dart';

class UpdateCheckerService {
  static final UpdateCheckerService _instance = UpdateCheckerService._internal();
  factory UpdateCheckerService() => _instance;
  UpdateCheckerService._internal();

  // Google Play Store API endpoint pour vérifier la version
  static const String _playStoreApiUrl = 'https://play.google.com/store/apps/details?id=';
  static const String _packageName = 'com.my.cleribccp'; // Remplacez par votre package name

  /// Vérifie s'il y a une nouvelle version disponible
  Future<bool> checkForUpdate() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      
      // Simuler la vérification avec Google Play Store
      // En production, vous devriez utiliser une API réelle ou un service comme Firebase Remote Config
      final latestVersion = await getLatestVersionFromPlayStore();
      
      if (latestVersion != null) {
        return _isNewerVersion(currentVersion, latestVersion);
      }
      
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la vérification de mise à jour: $e');
      return false;
    }
  }

  /// Récupère la dernière version depuis le Play Store (simulation)
  Future<String?> getLatestVersionFromPlayStore() async {
    try {
      // Pour le test, nous simulons une version plus récente
      // En production, vous devriez utiliser l'API Google Play Developer ou Firebase Remote Config
      await Future.delayed(const Duration(seconds: 1)); // Simuler la latence réseau
      
      // Simulation : retourner une version plus récente pour tester
      return '2.1.3'; // Version simulée plus récente que 2.1.2
    } catch (e) {
      debugPrint('Erreur lors de la récupération de la version: $e');
      return null;
    }
  }

  /// Compare deux versions et détermine si la nouvelle est plus récente
  bool _isNewerVersion(String currentVersion, String latestVersion) {
    final current = _parseVersion(currentVersion);
    final latest = _parseVersion(latestVersion);
    
    for (int i = 0; i < 3; i++) {
      if (latest[i] > current[i]) return true;
      if (latest[i] < current[i]) return false;
    }
    
    return false;
  }

  /// Parse une version string en liste d'entiers
  List<int> _parseVersion(String version) {
    return version.split('.').map((e) => int.tryParse(e) ?? 0).toList();
  }

  /// Récupère le message de mise à jour localisé
  String _getUpdateMessage(AppLocalizations loc, String latestVersion) {
    final locale = loc.localeName;

    switch (locale) {
      case 'fr':
        return 'Une nouvelle version ($latestVersion) de CCP RIP DZ est disponible sur Google Play Store.';
      case 'ar':
        return 'إصدار جديد ($latestVersion) من CCP RIP DZ متاح على متجر Google Play.';
      case 'en':
      default:
        return 'A new version ($latestVersion) of CCP RIP DZ is available on Google Play Store.';
    }
  }

  /// Récupère le titre localisé
  String _getUpdateTitle(AppLocalizations loc) {
    final locale = loc.localeName;

    switch (locale) {
      case 'fr':
        return 'Mise à jour disponible';
      case 'ar':
        return 'تحديث متاح';
      case 'en':
      default:
        return 'Update Available';
    }
  }

  /// Récupère la recommandation localisée
  String _getUpdateRecommendation(AppLocalizations loc) {
    final locale = loc.localeName;

    switch (locale) {
      case 'fr':
        return 'Nous recommandons de mettre à jour pour bénéficier des dernières améliorations et corrections.';
      case 'ar':
        return 'نوصي بالتحديث للاستفادة من أحدث التحسينات والإصلاحات.';
      case 'en':
      default:
        return 'We recommend updating to benefit from the latest improvements and fixes.';
    }
  }

  /// Récupère le texte du bouton "Mettre à jour"
  String _getUpdateNowText(AppLocalizations loc) {
    final locale = loc.localeName;

    switch (locale) {
      case 'fr':
        return 'Mettre à jour';
      case 'ar':
        return 'تحديث الآن';
      case 'en':
      default:
        return 'Update Now';
    }
  }

  /// Récupère le texte du bouton "Plus tard"
  String _getUpdateLaterText(AppLocalizations loc) {
    final locale = loc.localeName;

    switch (locale) {
      case 'fr':
        return 'Plus tard';
      case 'ar':
        return 'لاحقاً';
      case 'en':
      default:
        return 'Later';
    }
  }

  /// Affiche le dialog de mise à jour avec le même design que "À propos"
  void showUpdateDialog(BuildContext context, String latestVersion) {
    final loc = AppLocalizations.of(context);
    
    showDialog(
      context: context,
      barrierDismissible: false, // L'utilisateur doit faire un choix
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.system_update,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _getUpdateTitle(loc),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getUpdateMessage(loc, latestVersion),
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getUpdateRecommendation(loc),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                _getUpdateLaterText(loc),
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _openPlayStore();
              },
              icon: const Icon(Icons.download, size: 18),
              label: Text(_getUpdateNowText(loc)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Ouvre le Play Store pour mettre à jour l'application
  Future<void> _openPlayStore() async {
    final url = 'https://play.google.com/store/apps/details?id=$_packageName';
    final uri = Uri.parse(url);
    
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Impossible d\'ouvrir le Play Store');
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'ouverture du Play Store: $e');
    }
  }

  /// Vérifie automatiquement les mises à jour au démarrage de l'app
  Future<void> checkForUpdateOnStartup(BuildContext context) async {
    // Attendre un peu pour que l'app soit complètement chargée
    await Future.delayed(const Duration(seconds: 3));
    
    if (!context.mounted) return;
    
    final hasUpdate = await checkForUpdate();
    if (hasUpdate && context.mounted) {
      final latestVersion = await getLatestVersionFromPlayStore();
      if (latestVersion != null) {
        showUpdateDialog(context, latestVersion);
      }
    }
  }
}
